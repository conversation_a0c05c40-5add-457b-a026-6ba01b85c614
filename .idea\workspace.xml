<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d7d20928-50bb-4673-aba6-95130a8b9383" name="Changes" comment="- modify test-type so it can be associated with a specific lab&#10;- modify test-type API to support the new changes&#10;- add new related tests" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <branch-grouping>
      <option value="directory" />
      <option value="repository" />
    </branch-grouping>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/backend" value="my-account-page" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/frontend" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zHw1yAeo5nurn49nr3DBmJmF0L" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Node.js.test-i18n.js.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "add-test-type",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/git/Sed-LIMS",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration name="test-i18n.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/frontend/public/assets/js/core/test-i18n.js" working-dir="$PROJECT_DIR$/frontend/public/assets/js/core">
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d7d20928-50bb-4673-aba6-95130a8b9383" name="Changes" comment="" />
      <created>1751401095016</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751401095016</updated>
      <workItem from="1751401114733" duration="185000" />
      <workItem from="1751421190262" duration="38000" />
      <workItem from="1751421246498" duration="442000" />
      <workItem from="1751461965129" duration="465000" />
      <workItem from="1751462458289" duration="775000" />
      <workItem from="1751463256617" duration="37000" />
      <workItem from="1751511105563" duration="61000" />
      <workItem from="1751511178192" duration="62000" />
      <workItem from="1751511252785" duration="463000" />
      <workItem from="1751511730477" duration="47000" />
      <workItem from="1751547076768" duration="1238000" />
      <workItem from="1751551614736" duration="11117000" />
      <workItem from="1751637656954" duration="1950000" />
      <workItem from="1751642111789" duration="3411000" />
      <workItem from="1751652418711" duration="2557000" />
      <workItem from="1751897127912" duration="13525000" />
      <workItem from="1751912659581" duration="2607000" />
      <workItem from="1751917539511" duration="537000" />
      <workItem from="1751918136690" duration="453000" />
      <workItem from="1751918682113" duration="1225000" />
      <workItem from="1751920743661" duration="3660000" />
      <workItem from="1751989628325" duration="421000" />
      <workItem from="1752003826730" duration="682000" />
      <workItem from="1752068642723" duration="5002000" />
      <workItem from="1752092736898" duration="109000" />
      <workItem from="1752092872676" duration="3128000" />
      <workItem from="1752096080982" duration="1688000" />
      <workItem from="1752153218758" duration="3790000" />
      <workItem from="1752157112024" duration="5000" />
      <workItem from="1752157160842" duration="186000" />
      <workItem from="1752157362001" duration="2407000" />
      <workItem from="1752159843533" duration="1364000" />
    </task>
    <task id="LOCAL-00001" summary="add test code for security methods, add docstring for test_auth_helpers.py">
      <option name="closed" value="true" />
      <created>1751574040325</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751574040325</updated>
    </task>
    <task id="LOCAL-00002" summary="- modify the req end point to support list req for both scientist and lab staff&#10;- add related test code for the new endpoints/methods">
      <option name="closed" value="true" />
      <created>1751575123376</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751575123376</updated>
    </task>
    <task id="LOCAL-00003" summary="Add tests for enhanced requisition CRUD with detailed response validation">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00004" summary="- create new homepage page&#10;    - display open table for lab admin * personnel&#10;    - display open and closed tables for scientist&#10;&#10;- create new view-requistion page&#10;    - display open and closed tables for any roles&#10;&#10;- modify links in the nav component so that user can nav to view-req, homepage, and my-account page correctly&#10;&#10;- implement the new JS architecture for homepae and view-req page&#10;&#10;- refractor the my-account page to the new JS architecture">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00005" summary="- modify test-type so it can be associated with a specific lab&#10;- modify test-type API to support the new changes&#10;- add new related tests">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="my-account-page" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add test code for security methods, add docstring for test_auth_helpers.py" />
    <MESSAGE value="- modify the req end point to support list req for both scientist and lab staff&#10;- add related test code for the new endpoints/methods" />
    <MESSAGE value="Add tests for enhanced requisition CRUD with detailed response validation" />
    <MESSAGE value="- create new homepage page&#10;    - display open table for lab admin * personnel&#10;    - display open and closed tables for scientist&#10;&#10;- create new view-requistion page&#10;    - display open and closed tables for any roles&#10;&#10;- modify links in the nav component so that user can nav to view-req, homepage, and my-account page correctly&#10;&#10;- implement the new JS architecture for homepae and view-req page&#10;&#10;- refractor the my-account page to the new JS architecture" />
    <MESSAGE value="- modify test-type so it can be associated with a specific lab&#10;- modify test-type API to support the new changes&#10;- add new related tests" />
    <option name="LAST_COMMIT_MESSAGE" value="- modify test-type so it can be associated with a specific lab&#10;- modify test-type API to support the new changes&#10;- add new related tests" />
  </component>
</project>