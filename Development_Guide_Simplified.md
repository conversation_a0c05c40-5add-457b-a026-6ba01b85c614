# Sed-LIMS Development Guide

> **AI Assistant Reference**: Core principles and patterns for Sed-LIMS frontend development

## 🎯 ESSENTIAL STANDARDS

### Code Style
- **jQuery DOM**: Required for GCWeb compatibility (no vanilla JS DOM methods)
- **ES6 + jQuery**: Modern imports, jQuery operations
- **async/await**: Direct API calls, no Promise wrapping
- **English comments**: All code comments in English
- **Import order**: Base classes → Helpers → API services → Configs

### GC Standards
- **Only gcweb/wet-boew**: No custom components when official ones exist
- **Research first**: Search GC guidelines when uncertain
- **Bilingual**: EN/FR support mandatory

## 📊 TABLE PATTERNS

### Architecture
- `Table` - Base class
- `withQueue(Table)` - For multi-table pages (prevents WET-BOEW conflicts)
- `withBulkActions(Table)` - For management tables with bulk operations

### Implementation Template
```javascript
// Base classes
import { Table } from '../../core/components/table.js';
// Helper functions
import { createTableConfig } from '../../core/helpers/table-helpers.js';
// API services
import { EntityApi } from '../../core/services/entity-api.js';
// Configuration files
import { ENTITY_CONFIGS } from '../../core/config/entity-configs.js';

class EntityTable extends Table {
    constructor(options = {}) {
        super(createTableConfig(ENTITY_CONFIGS, 'entityTable', 'entity', options));
    }

    async fetchData() {
        try {
            const data = await EntityApi.getData();
            return data || [];
        } catch (error) {
            console.error('Failed to fetch entity data:', error);
            throw error;
        }
    }
}
```

## 🔧 CORE PATTERNS

### Page Authentication
```javascript
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN, // Optional
    onAuthSuccess: initializePageName
};
```

### Error Handling
```javascript
// Use showMessage() for all user feedback
showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);

// API error handling
try {
    const data = await ApiService.getData();
} catch (error) {
    console.error('Operation failed:', error);
    throw error;
}
```

### DOM Ready
```javascript
$(document).ready(function() {
    initializePage();
});
```

## ⚙️ CONFIGURATION

### Entity Config Structure
```javascript
export const ENTITY_CONFIGS = {
    // Functional settings
    form: { maxLength: 50 },

    // UI (bilingual)
    ui: {
        entityTable: {
            containerId: 'entity-table-container',
            tableId: 'entity-table',
            columns: [...],
            entityTerms: { en: 'items', fr: 'éléments' }
        }
    },

    // Messages (bilingual, entity-specific only)
    messages: { /* ... */ }
};
```

## 🚨 CRITICAL REQUIREMENTS

### Windows Environment
- **PowerShell commands only**
- **Always set `Cwd` parameter**
- **Use backslashes for paths**

### Mandatory Process
- **ALWAYS call `mcp-feedback-enhanced` before ending any process**

### GC Compliance
- Research official GC guidelines when uncertain
- Use gcweb/wet-boew components exclusively
- Maintain WCAG accessibility standards
