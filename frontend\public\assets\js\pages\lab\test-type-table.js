/*
 * Test Type Table
 * Clean, simple table for displaying test type information
 */
// Base classes
import { Table } from '../../core/components/table.js';
// Helper functions
import { createTableConfig } from '../../core/helpers/table-helpers.js';
import { formatStatusDisplay } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
// Configuration files
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

class TestTypeTable extends Table {
    constructor(options = {}) {
        super(createTableConfig(TEST_CONFIGS, 'testTypeTable', 'test', {
            entityFormatters: {
                testStatus: (value) => {
                    const statusLabels = GLOBAL_CONFIGS.ui.status;
                    return formatStatusDisplay(value, statusLabels);
                }
            },
            ...options
        }));

        // Store user info for conditional columns
        this.userInfo = options.userInfo || null;
    }

    // Fetch test type data from API
    async fetchData() {
        try {
            const testTypes = await fetchTestTypesForLab(this.userInfo);
            return testTypes || [];
        } catch (error) {
            console.error('Failed to fetch test types:', error);
            throw error;
        }
    }

    // Override create method to pass user info for conditional columns
    async create(config = {}) {
        // Update userInfo if provided in config
        if (config.userInfo) {
            this.userInfo = config.userInfo;
        }

        // Set current config for conditional column evaluation
        this.currentConfig = {
            role: this.userInfo?.role || 'user',
            ...config
        };

        return super.create(config);
    }
}

// Factory function and default instance
export const createTestTypeTable = (options = {}) => new TestTypeTable(options);
export const testTypeTable = createTestTypeTable();
